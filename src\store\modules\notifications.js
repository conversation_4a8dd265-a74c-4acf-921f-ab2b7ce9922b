import axios from 'axios'
import usuariosService from '@/services/usuariosService'

// Configurar axios para usar as mesmas configurações do serviço principal
const { API_URL } = require('@/config.json')
axios.defaults.baseURL = API_URL
axios.defaults.withCredentials = true

// Interceptor para adicionar token automaticamente
axios.interceptors.request.use(request => {
  const token = usuariosService.getAuthToken()
  if (token) {
    request.headers.Authorization = `Bearer ${token}`
  }
  return request
}, error => {
  return Promise.reject(error)
})

const state = {
  notifications: [],
  unreadCount: 0,
  loading: false,
  refreshing: false, // Para refresh sem limpar a lista
  error: null,
  lastFetch: null,
  pollingInterval: null
}

const mutations = {
  SET_NOTIFICATIONS(state, notifications) {
    state.notifications = notifications
  },
  
  SET_UNREAD_COUNT(state, count) {
    state.unreadCount = count
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading
  },

  SET_REFRESHING(state, refreshing) {
    state.refreshing = refreshing
  },
  
  SET_ERROR(state, error) {
    state.error = error
  },
  
  SET_LAST_FETCH(state, timestamp) {
    state.lastFetch = timestamp
  },
  
  SET_POLLING_INTERVAL(state, interval) {
    state.pollingInterval = interval
  },
  
  ADD_NOTIFICATION(state, notification) {
    state.notifications.unshift(notification)
    if (!notification.read) {
      state.unreadCount++
    }
  },
  
  UPDATE_NOTIFICATION(state, updatedNotification) {
    const index = state.notifications.findIndex(n => n.id === updatedNotification.id)
    if (index !== -1) {
      const wasUnread = !state.notifications[index].read
      const isNowRead = updatedNotification.read
      
      state.notifications.splice(index, 1, updatedNotification)
      
      // Atualizar contador se mudou o status de leitura
      if (wasUnread && isNowRead) {
        state.unreadCount = Math.max(0, state.unreadCount - 1)
      } else if (!wasUnread && !isNowRead) {
        state.unreadCount++
      }
    }
  },
  
  REMOVE_NOTIFICATION(state, notificationId) {
    const index = state.notifications.findIndex(n => n.id === notificationId)
    if (index !== -1) {
      const notification = state.notifications[index]
      if (!notification.read) {
        state.unreadCount = Math.max(0, state.unreadCount - 1)
      }
      state.notifications.splice(index, 1)
    }
  },
  
  MARK_ALL_AS_READ(state) {
    state.notifications.forEach(notification => {
      if (!notification.read) {
        notification.read = true
        notification.read_at = new Date().toISOString()
      }
    })
    state.unreadCount = 0
  },
  
  CLEAR_ERROR(state) {
    state.error = null
  }
}

const actions = {
  async fetchNotifications({ commit, state }, { page = 1, perPage = 15, read = null, isRefresh = false } = {}) {
    // Se já há notificações e é um refresh, usar refreshing em vez de loading
    const hasExistingNotifications = state.notifications.length > 0

    if (hasExistingNotifications && isRefresh) {
      commit('SET_REFRESHING', true)
    } else {
      commit('SET_LOADING', true)
    }

    commit('CLEAR_ERROR')

    try {
      const params = { page, per_page: perPage }
      if (read !== null) {
        params.read = read
      }

      const response = await axios.get('/notifications', { params })

      if (response && response.data && response.data.status === 'success') {
        commit('SET_NOTIFICATIONS', response.data.data.data || [])
        commit('SET_LAST_FETCH', Date.now())
        return response.data.data
      }

      throw new Error('Resposta inválida do servidor')
    } catch (error) {
      console.error('Erro ao buscar notificações:', error)
      commit('SET_ERROR', 'Erro ao carregar notificações')
      throw error
    } finally {
      if (hasExistingNotifications && isRefresh) {
        commit('SET_REFRESHING', false)
      } else {
        commit('SET_LOADING', false)
      }
    }
  },
  
  async fetchUnreadCount({ commit }) {
    try {
      const response = await axios.get('/notifications/unread-count')
      
      if (response && response.data && response.data.status === 'success') {
        commit('SET_UNREAD_COUNT', response.data.data.count || 0)
        return response.data.data.count
      }
      
      throw new Error('Resposta inválida do servidor')
    } catch (error) {
      console.error('Erro ao buscar contagem de não lidas:', error)
      commit('SET_ERROR', 'Erro ao carregar contagem de notificações')
      throw error
    }
  },
  
  async markAsRead({ commit }, notificationId) {
    try {
      const response = await axios.put(`/notifications/${notificationId}`, { read: true })
      
      if (response && response.data && response.data.status === 'success') {
        commit('UPDATE_NOTIFICATION', response.data.data)
        return response.data.data
      }
      
      throw new Error('Resposta inválida do servidor')
    } catch (error) {
      console.error('Erro ao marcar como lida:', error)
      commit('SET_ERROR', 'Erro ao marcar notificação como lida')
      throw error
    }
  },
  
  async markAsUnread({ commit }, notificationId) {
    try {
      const response = await axios.put(`/notifications/${notificationId}`, { read: false })
      
      if (response && response.data && response.data.status === 'success') {
        commit('UPDATE_NOTIFICATION', response.data.data)
        return response.data.data
      }
      
      throw new Error('Resposta inválida do servidor')
    } catch (error) {
      console.error('Erro ao marcar como não lida:', error)
      commit('SET_ERROR', 'Erro ao marcar notificação como não lida')
      throw error
    }
  },
  
  async markAllAsRead({ commit }) {
    try {
      const response = await axios.post('/notifications/mark-all-read')
      
      if (response && response.data && response.data.status === 'success') {
        commit('MARK_ALL_AS_READ')
        return response.data
      }
      
      throw new Error('Resposta inválida do servidor')
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error)
      commit('SET_ERROR', 'Erro ao marcar todas as notificações como lidas')
      throw error
    }
  },
  
  async deleteNotification({ commit }, notificationId) {
    try {
      const response = await axios.delete(`/notifications/${notificationId}`)
      
      if (response && response.data && response.data.status === 'success') {
        commit('REMOVE_NOTIFICATION', notificationId)
        return response.data
      }
      
      throw new Error('Resposta inválida do servidor')
    } catch (error) {
      console.error('Erro ao deletar notificação:', error)
      commit('SET_ERROR', 'Erro ao deletar notificação')
      throw error
    }
  },
  
  async createNotification({ commit }, notificationData) {
    try {
      const response = await axios.post('/notifications', notificationData)
      
      if (response && response.data && response.data.status === 'success') {
        commit('ADD_NOTIFICATION', response.data.data)
        return response.data.data
      }
      
      throw new Error('Resposta inválida do servidor')
    } catch (error) {
      console.error('Erro ao criar notificação:', error)
      commit('SET_ERROR', 'Erro ao criar notificação')
      throw error
    }
  },
  
  startPolling({ dispatch, commit, state }, interval = 30000) {
    if (state.pollingInterval) {
      clearInterval(state.pollingInterval)
    }
    
    const pollingId = setInterval(() => {
      dispatch('fetchUnreadCount')
    }, interval)
    
    commit('SET_POLLING_INTERVAL', pollingId)
  },
  
  stopPolling({ commit, state }) {
    if (state.pollingInterval) {
      clearInterval(state.pollingInterval)
      commit('SET_POLLING_INTERVAL', null)
    }
  },
  
  clearError({ commit }) {
    commit('CLEAR_ERROR')
  }
}

const getters = {
  allNotifications: state => state.notifications,
  unreadNotifications: state => state.notifications.filter(n => !n.read),
  readNotifications: state => state.notifications.filter(n => n.read),
  unreadCount: state => state.unreadCount,
  isLoading: state => state.loading,
  isRefreshing: state => state.refreshing,
  error: state => state.error,
  hasNotifications: state => state.notifications.length > 0,
  hasUnreadNotifications: state => state.unreadCount > 0,
  lastFetch: state => state.lastFetch,
  
  getNotificationById: state => id => {
    return state.notifications.find(n => n.id === id)
  },
  
  getNotificationsByType: state => type => {
    return state.notifications.filter(n => n.type === type)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
